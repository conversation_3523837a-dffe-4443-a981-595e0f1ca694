# 应用配置文件
app:
  name: "量化交易数据处理系统"
  version: "1.0.0"

# 数据库配置
database:
  url: "*******************************************************************************************************"
  username: "root"
  password: "password"
  driver: "com.mysql.cj.jdbc.Driver"

# 百度网盘配置
baidu:
  # 官方API配置（需要企业认证）
  api:
    app_key: "your_app_key"
    secret_key: "your_secret_key"
    redirect_uri: "http://localhost:8080/callback"
    
  # 第三方工具配置
  pcs:
    tool_path: "/usr/local/bin/BaiduPCS-Go"  # BaiduPCS-Go工具路径
    config_dir: "~/.config/BaiduPCS-Go"     # 配置目录
    
  # 分享链接配置
  share:
    url: "https://pan.baidu.com/s/1kY8OyapYGLc9CwPy1ik8Iw"
    password: "6666"

# 下载配置
download:
  temp_dir: "./temp"           # 临时下载目录
  target_dir: "./data"         # 目标数据目录
  max_retry: 3                 # 最大重试次数
  timeout: 300000              # 超时时间（毫秒）
  
# 数据处理配置
data:
  batch_size: 1000             # 批量处理大小
  supported_formats:           # 支持的文件格式
    - "csv"
    - "xlsx"
    - "xls"
    - "json"
    - "txt"

# 日志配置
logging:
  level: "INFO"
  file: "./logs/app.log"
