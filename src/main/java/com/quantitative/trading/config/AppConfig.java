package com.quantitative.trading.config;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.yaml.snakeyaml.Yaml;

import javax.sql.DataSource;
import java.io.InputStream;
import java.util.Map;

/**
 * 应用配置类
 * 负责加载配置文件和初始化数据源
 */
public class AppConfig {
    
    private static AppConfig instance;
    private Map<String, Object> config;
    private DataSource dataSource;
    
    private AppConfig() {
        loadConfig();
        initDataSource();
    }
    
    public static synchronized AppConfig getInstance() {
        if (instance == null) {
            instance = new AppConfig();
        }
        return instance;
    }
    
    @SuppressWarnings("unchecked")
    private void loadConfig() {
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream("application.yml")) {
            if (inputStream == null) {
                throw new RuntimeException("配置文件 application.yml 未找到");
            }
            Yaml yaml = new Yaml();
            config = yaml.load(inputStream);
        } catch (Exception e) {
            throw new RuntimeException("加载配置文件失败", e);
        }
    }
    
    @SuppressWarnings("unchecked")
    private void initDataSource() {
        Map<String, Object> dbConfig = (Map<String, Object>) config.get("database");
        if (dbConfig == null) {
            throw new RuntimeException("数据库配置未找到");
        }
        
        HikariConfig hikariConfig = new HikariConfig();
        hikariConfig.setJdbcUrl((String) dbConfig.get("url"));
        hikariConfig.setUsername((String) dbConfig.get("username"));
        hikariConfig.setPassword((String) dbConfig.get("password"));
        hikariConfig.setDriverClassName((String) dbConfig.get("driver"));
        
        // 连接池配置
        hikariConfig.setMaximumPoolSize(10);
        hikariConfig.setMinimumIdle(2);
        hikariConfig.setConnectionTimeout(30000);
        hikariConfig.setIdleTimeout(600000);
        hikariConfig.setMaxLifetime(1800000);
        
        this.dataSource = new HikariDataSource(hikariConfig);
    }
    
    public DataSource getDataSource() {
        return dataSource;
    }
    
    @SuppressWarnings("unchecked")
    public Map<String, Object> getBaiduConfig() {
        return (Map<String, Object>) config.get("baidu");
    }
    
    @SuppressWarnings("unchecked")
    public Map<String, Object> getDownloadConfig() {
        return (Map<String, Object>) config.get("download");
    }
    
    public String getProperty(String key) {
        return getProperty(key, null);
    }
    
    @SuppressWarnings("unchecked")
    public String getProperty(String key, String defaultValue) {
        String[] keys = key.split("\\.");
        Object current = config;
        
        for (String k : keys) {
            if (current instanceof Map) {
                current = ((Map<String, Object>) current).get(k);
            } else {
                return defaultValue;
            }
        }
        
        return current != null ? current.toString() : defaultValue;
    }
}
