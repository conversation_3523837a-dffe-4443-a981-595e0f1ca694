package com.quantitative.trading.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.quantitative.trading.config.AppConfig;
import com.quantitative.trading.model.FileInfo;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;

/**
 * 数据处理服务类
 * 负责解析各种格式的数据文件
 */
public class DataProcessorService {
    
    private static final Logger logger = LoggerFactory.getLogger(DataProcessorService.class);
    private final AppConfig config;
    private final ObjectMapper objectMapper;
    
    public DataProcessorService() {
        this.config = AppConfig.getInstance();
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * 处理文件并提取数据
     * @param fileInfo 文件信息
     * @return 提取的数据列表
     */
    public List<Map<String, Object>> processFile(FileInfo fileInfo) {
        logger.info("开始处理文件: {}", fileInfo.getFileName());
        
        List<Map<String, Object>> data = new ArrayList<>();
        
        try {
            String fileType = fileInfo.getFileType().toLowerCase();
            String filePath = fileInfo.getFilePath();
            
            switch (fileType) {
                case "csv":
                    data = processCsvFile(filePath);
                    break;
                case "xlsx":
                case "xls":
                    data = processExcelFile(filePath);
                    break;
                case "json":
                    data = processJsonFile(filePath);
                    break;
                case "txt":
                    data = processTextFile(filePath);
                    break;
                default:
                    logger.warn("不支持的文件格式: {}", fileType);
                    break;
            }
            
            fileInfo.setStatus("PROCESSED");
            logger.info("文件处理完成，提取了 {} 条数据", data.size());
            
        } catch (Exception e) {
            logger.error("处理文件失败: {}", fileInfo.getFileName(), e);
            fileInfo.setStatus("ERROR");
            fileInfo.setErrorMessage(e.getMessage());
        }
        
        return data;
    }
    
    /**
     * 处理CSV文件
     */
    private List<Map<String, Object>> processCsvFile(String filePath) throws IOException {
        List<Map<String, Object>> data = new ArrayList<>();
        
        try (Reader reader = Files.newBufferedReader(Paths.get(filePath), StandardCharsets.UTF_8);
             CSVParser csvParser = new CSVParser(reader, CSVFormat.DEFAULT.withFirstRecordAsHeader())) {
            
            for (CSVRecord csvRecord : csvParser) {
                Map<String, Object> record = new HashMap<>();
                
                // 获取所有列名
                Set<String> headers = csvParser.getHeaderMap().keySet();
                
                for (String header : headers) {
                    String value = csvRecord.get(header);
                    record.put(header, parseValue(value));
                }
                
                data.add(record);
            }
        }
        
        return data;
    }
    
    /**
     * 处理Excel文件
     */
    private List<Map<String, Object>> processExcelFile(String filePath) throws IOException {
        List<Map<String, Object>> data = new ArrayList<>();
        
        try (FileInputStream fis = new FileInputStream(filePath);
             Workbook workbook = new XSSFWorkbook(fis)) {
            
            Sheet sheet = workbook.getSheetAt(0); // 读取第一个工作表
            Iterator<Row> rowIterator = sheet.iterator();
            
            // 读取表头
            List<String> headers = new ArrayList<>();
            if (rowIterator.hasNext()) {
                Row headerRow = rowIterator.next();
                for (Cell cell : headerRow) {
                    headers.add(getCellValueAsString(cell));
                }
            }
            
            // 读取数据行
            while (rowIterator.hasNext()) {
                Row row = rowIterator.next();
                Map<String, Object> record = new HashMap<>();
                
                for (int i = 0; i < headers.size() && i < row.getLastCellNum(); i++) {
                    Cell cell = row.getCell(i);
                    String header = headers.get(i);
                    Object value = getCellValue(cell);
                    record.put(header, value);
                }
                
                data.add(record);
            }
        }
        
        return data;
    }
    
    /**
     * 处理JSON文件
     */
    private List<Map<String, Object>> processJsonFile(String filePath) throws IOException {
        List<Map<String, Object>> data = new ArrayList<>();
        
        String content = Files.readString(Paths.get(filePath), StandardCharsets.UTF_8);
        JsonNode rootNode = objectMapper.readTree(content);
        
        if (rootNode.isArray()) {
            // JSON数组
            for (JsonNode node : rootNode) {
                Map<String, Object> record = objectMapper.convertValue(node, Map.class);
                data.add(record);
            }
        } else if (rootNode.isObject()) {
            // 单个JSON对象
            Map<String, Object> record = objectMapper.convertValue(rootNode, Map.class);
            data.add(record);
        }
        
        return data;
    }
    
    /**
     * 处理文本文件
     */
    private List<Map<String, Object>> processTextFile(String filePath) throws IOException {
        List<Map<String, Object>> data = new ArrayList<>();
        
        List<String> lines = Files.readAllLines(Paths.get(filePath), StandardCharsets.UTF_8);
        
        for (int i = 0; i < lines.size(); i++) {
            Map<String, Object> record = new HashMap<>();
            record.put("line_number", i + 1);
            record.put("content", lines.get(i));
            data.add(record);
        }
        
        return data;
    }
    
    /**
     * 获取Excel单元格的值
     */
    private Object getCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue();
                } else {
                    return cell.getNumericCellValue();
                }
            case BOOLEAN:
                return cell.getBooleanCellValue();
            case FORMULA:
                return cell.getCellFormula();
            default:
                return null;
        }
    }
    
    /**
     * 获取Excel单元格的字符串值
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }
    
    /**
     * 解析字符串值，尝试转换为合适的数据类型
     */
    private Object parseValue(String value) {
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        
        value = value.trim();
        
        // 尝试解析为数字
        try {
            if (value.contains(".")) {
                return Double.parseDouble(value);
            } else {
                return Long.parseLong(value);
            }
        } catch (NumberFormatException e) {
            // 不是数字，继续其他类型的判断
        }
        
        // 尝试解析为布尔值
        if ("true".equalsIgnoreCase(value) || "false".equalsIgnoreCase(value)) {
            return Boolean.parseBoolean(value);
        }
        
        // 默认返回字符串
        return value;
    }
}
