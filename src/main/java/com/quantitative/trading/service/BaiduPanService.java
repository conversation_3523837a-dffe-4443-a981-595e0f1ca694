package com.quantitative.trading.service;

import com.quantitative.trading.config.AppConfig;
import com.quantitative.trading.model.FileInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 百度网盘服务类
 * 负责处理百度网盘文件的下载
 */
public class BaiduPanService {
    
    private static final Logger logger = LoggerFactory.getLogger(BaiduPanService.class);
    private final AppConfig config;
    
    public BaiduPanService() {
        this.config = AppConfig.getInstance();
    }
    
    /**
     * 下载分享链接中的文件
     * @param shareUrl 分享链接
     * @param password 提取码
     * @return 下载的文件列表
     */
    public List<FileInfo> downloadFromShare(String shareUrl, String password) {
        logger.info("开始下载百度网盘分享文件: {}", shareUrl);
        
        List<FileInfo> downloadedFiles = new ArrayList<>();
        
        try {
            // 创建临时下载目录
            String tempDir = config.getProperty("download.temp_dir", "./temp");
            createDirectoryIfNotExists(tempDir);
            
            // 方案1: 尝试使用官方API（如果配置了的话）
            Map<String, Object> baiduConfig = config.getBaiduConfig();
            if (baiduConfig != null && baiduConfig.containsKey("api")) {
                logger.info("尝试使用百度网盘官方API下载");
                downloadedFiles = downloadUsingOfficialAPI(shareUrl, password, tempDir);
            }
            
            // 方案2: 如果官方API不可用，使用第三方工具
            if (downloadedFiles.isEmpty()) {
                logger.info("使用第三方工具下载");
                downloadedFiles = downloadUsingThirdPartyTool(shareUrl, password, tempDir);
            }
            
            // 方案3: 如果都不可用，提示用户手动下载
            if (downloadedFiles.isEmpty()) {
                logger.warn("自动下载失败，请手动下载文件到目录: {}", tempDir);
                downloadedFiles = scanManuallyDownloadedFiles(tempDir);
            }
            
        } catch (Exception e) {
            logger.error("下载文件时发生错误", e);
        }
        
        return downloadedFiles;
    }
    
    /**
     * 使用官方API下载（需要企业认证）
     */
    private List<FileInfo> downloadUsingOfficialAPI(String shareUrl, String password, String tempDir) {
        logger.info("官方API下载功能开发中...");
        // TODO: 实现官方API下载逻辑
        // 1. 获取access_token
        // 2. 解析分享链接
        // 3. 获取文件列表
        // 4. 下载文件
        return new ArrayList<>();
    }
    
    /**
     * 使用第三方工具下载
     */
    private List<FileInfo> downloadUsingThirdPartyTool(String shareUrl, String password, String tempDir) {
        List<FileInfo> files = new ArrayList<>();

        try {
            logger.info("尝试使用多种方法下载百度网盘文件");

            // 方法1: 尝试使用BaiduPCS-Go
            if (isBaiduPCSGoAvailable()) {
                logger.info("尝试使用BaiduPCS-Go下载");
                files = downloadWithBaiduPCSGo(shareUrl, password, tempDir);
            }

            // 方法2: 如果BaiduPCS-Go不可用，尝试使用curl下载（需要解析分享页面）
            if (files.isEmpty()) {
                logger.info("尝试使用网页解析方法");
                files = downloadWithWebParsing(shareUrl, password, tempDir);
            }

            // 方法3: 如果都不行，提供手动下载指导
            if (files.isEmpty()) {
                logger.info("自动下载方法都不可用，提供手动下载指导");
                files = guideManualDownload(shareUrl, password, tempDir);
            }

        } catch (Exception e) {
            logger.error("使用第三方工具下载失败", e);
        }

        return files;
    }

    /**
     * 使用BaiduPCS-Go下载
     */
    private List<FileInfo> downloadWithBaiduPCSGo(String shareUrl, String password, String tempDir) {
        List<FileInfo> files = new ArrayList<>();

        try {
            String toolPath = config.getProperty("baidu.pcs.tool_path", "BaiduPCS-Go");

            // BaiduPCS-Go的分享链接下载命令格式
            ProcessBuilder pb = new ProcessBuilder(
                toolPath,
                "share",
                "download",
                shareUrl,
                "--password", password,
                "--saveto", tempDir
            );

            pb.redirectErrorStream(true);
            Process process = pb.start();

            // 读取输出
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    logger.info("BaiduPCS-Go输出: {}", line);
                }
            }

            int exitCode = process.waitFor();
            if (exitCode == 0) {
                logger.info("BaiduPCS-Go下载完成，扫描下载的文件");
                files = scanDownloadedFiles(tempDir);
            } else {
                logger.error("BaiduPCS-Go下载失败，退出码: {}", exitCode);
            }

        } catch (Exception e) {
            logger.error("BaiduPCS-Go下载失败", e);
        }

        return files;
    }

    /**
     * 使用网页解析方法下载
     */
    private List<FileInfo> downloadWithWebParsing(String shareUrl, String password, String tempDir) {
        List<FileInfo> files = new ArrayList<>();

        try {
            logger.info("网页解析下载方法开发中...");
            // TODO: 实现网页解析下载
            // 1. 访问分享页面
            // 2. 提交提取码
            // 3. 解析文件列表
            // 4. 获取下载链接
            // 5. 下载文件

            // 这里可以使用HttpClient来实现网页解析和文件下载
            // 由于百度网盘的反爬虫机制，这个方法比较复杂

        } catch (Exception e) {
            logger.error("网页解析下载失败", e);
        }

        return files;
    }

    /**
     * 指导用户手动下载
     */
    private List<FileInfo> guideManualDownload(String shareUrl, String password, String tempDir) {
        logger.info("=== 手动下载指导 ===");
        logger.info("由于自动下载方法不可用，请按照以下步骤手动下载：");
        logger.info("1. 在浏览器中打开链接: {}", shareUrl);
        logger.info("2. 输入提取码: {}", password);
        logger.info("3. 选择所有文件并下载到目录: {}", tempDir);
        logger.info("4. 下载完成后按回车键继续...");

        System.out.println("\n=== 手动下载指导 ===");
        System.out.println("1. 在浏览器中打开链接: " + shareUrl);
        System.out.println("2. 输入提取码: " + password);
        System.out.println("3. 选择所有文件并下载到目录: " + tempDir);
        System.out.println("4. 下载完成后按回车键继续...");

        try {
            System.in.read();
        } catch (IOException e) {
            logger.error("读取用户输入失败", e);
        }

        return scanDownloadedFiles(tempDir);
    }
    
    /**
     * 检查BaiduPCS-Go是否可用
     */
    private boolean isBaiduPCSGoAvailable() {
        try {
            String toolPath = config.getProperty("baidu.pcs.tool_path", "BaiduPCS-Go");
            ProcessBuilder pb = new ProcessBuilder(toolPath, "--version");
            Process process = pb.start();
            int exitCode = process.waitFor();
            return exitCode == 0;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 扫描下载的文件
     */
    private List<FileInfo> scanDownloadedFiles(String directory) {
        return scanFilesInDirectory(directory, "DOWNLOADED");
    }
    
    /**
     * 扫描手动下载的文件
     */
    private List<FileInfo> scanManuallyDownloadedFiles(String directory) {
        logger.info("请将文件手动下载到目录: {}", directory);
        logger.info("下载完成后，程序将自动扫描文件");
        
        // 等待用户手动下载
        System.out.println("请将百度网盘文件下载到目录: " + directory);
        System.out.println("下载完成后按回车键继续...");
        try {
            System.in.read();
        } catch (IOException e) {
            logger.error("读取用户输入失败", e);
        }
        
        return scanFilesInDirectory(directory, "MANUAL");
    }
    
    /**
     * 扫描目录中的文件
     */
    private List<FileInfo> scanFilesInDirectory(String directory, String status) {
        List<FileInfo> files = new ArrayList<>();
        
        try {
            Path dirPath = Paths.get(directory);
            if (!Files.exists(dirPath)) {
                logger.warn("目录不存在: {}", directory);
                return files;
            }
            
            Files.walk(dirPath)
                    .filter(Files::isRegularFile)
                    .forEach(path -> {
                        File file = path.toFile();
                        String fileName = file.getName();
                        String fileType = getFileExtension(fileName);
                        
                        FileInfo fileInfo = new FileInfo(
                                fileName,
                                file.getAbsolutePath(),
                                file.length(),
                                fileType
                        );
                        fileInfo.setStatus(status);
                        
                        files.add(fileInfo);
                        logger.info("发现文件: {}", fileInfo);
                    });
                    
        } catch (Exception e) {
            logger.error("扫描目录失败: {}", directory, e);
        }
        
        return files;
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1).toLowerCase();
        }
        return "";
    }
    
    /**
     * 创建目录（如果不存在）
     */
    private void createDirectoryIfNotExists(String directory) throws IOException {
        Path path = Paths.get(directory);
        if (!Files.exists(path)) {
            Files.createDirectories(path);
            logger.info("创建目录: {}", directory);
        }
    }
}
