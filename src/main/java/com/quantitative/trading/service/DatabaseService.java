package com.quantitative.trading.service;

import com.quantitative.trading.config.AppConfig;
import com.quantitative.trading.model.FileInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sql.DataSource;
import java.sql.*;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 数据库服务类
 * 负责数据的存储和查询
 */
public class DatabaseService {
    
    private static final Logger logger = LoggerFactory.getLogger(DatabaseService.class);
    private final DataSource dataSource;
    
    public DatabaseService() {
        this.dataSource = AppConfig.getInstance().getDataSource();
        initializeTables();
    }
    
    /**
     * 初始化数据库表
     */
    private void initializeTables() {
        try (Connection conn = dataSource.getConnection()) {
            // 创建文件信息表
            createFileInfoTable(conn);
            
            // 创建数据表（通用的键值对存储）
            createDataTable(conn);
            
            logger.info("数据库表初始化完成");
            
        } catch (SQLException e) {
            logger.error("初始化数据库表失败", e);
            throw new RuntimeException("数据库初始化失败", e);
        }
    }
    
    /**
     * 创建文件信息表
     */
    private void createFileInfoTable(Connection conn) throws SQLException {
        String sql = """
            CREATE TABLE IF NOT EXISTS file_info (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                file_name VARCHAR(255) NOT NULL,
                file_path VARCHAR(500) NOT NULL,
                file_size BIGINT NOT NULL,
                file_type VARCHAR(50) NOT NULL,
                download_time TIMESTAMP NOT NULL,
                status VARCHAR(50) NOT NULL,
                error_message TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
            """;
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.executeUpdate();
            logger.info("文件信息表创建/检查完成");
        }
    }
    
    /**
     * 创建数据表
     */
    private void createDataTable(Connection conn) throws SQLException {
        String sql = """
            CREATE TABLE IF NOT EXISTS trading_data (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                file_id BIGINT,
                data_key VARCHAR(255) NOT NULL,
                data_value TEXT,
                data_type VARCHAR(50),
                row_number INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_file_id (file_id),
                INDEX idx_data_key (data_key),
                FOREIGN KEY (file_id) REFERENCES file_info(id) ON DELETE CASCADE
            )
            """;
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.executeUpdate();
            logger.info("交易数据表创建/检查完成");
        }
    }
    
    /**
     * 保存文件信息
     */
    public Long saveFileInfo(FileInfo fileInfo) {
        String sql = """
            INSERT INTO file_info (file_name, file_path, file_size, file_type, download_time, status, error_message)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            """;
        
        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            
            stmt.setString(1, fileInfo.getFileName());
            stmt.setString(2, fileInfo.getFilePath());
            stmt.setLong(3, fileInfo.getFileSize());
            stmt.setString(4, fileInfo.getFileType());
            stmt.setTimestamp(5, Timestamp.valueOf(fileInfo.getDownloadTime()));
            stmt.setString(6, fileInfo.getStatus());
            stmt.setString(7, fileInfo.getErrorMessage());
            
            int affectedRows = stmt.executeUpdate();
            
            if (affectedRows > 0) {
                try (ResultSet generatedKeys = stmt.getGeneratedKeys()) {
                    if (generatedKeys.next()) {
                        Long fileId = generatedKeys.getLong(1);
                        logger.info("文件信息保存成功，ID: {}", fileId);
                        return fileId;
                    }
                }
            }
            
        } catch (SQLException e) {
            logger.error("保存文件信息失败: {}", fileInfo.getFileName(), e);
        }
        
        return null;
    }
    
    /**
     * 更新文件信息状态
     */
    public void updateFileStatus(Long fileId, String status, String errorMessage) {
        String sql = "UPDATE file_info SET status = ?, error_message = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
        
        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, status);
            stmt.setString(2, errorMessage);
            stmt.setLong(3, fileId);
            
            int affectedRows = stmt.executeUpdate();
            if (affectedRows > 0) {
                logger.info("文件状态更新成功，ID: {}, 状态: {}", fileId, status);
            }
            
        } catch (SQLException e) {
            logger.error("更新文件状态失败，ID: {}", fileId, e);
        }
    }
    
    /**
     * 批量保存数据
     */
    public void saveData(Long fileId, List<Map<String, Object>> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            logger.warn("没有数据需要保存");
            return;
        }
        
        String sql = """
            INSERT INTO trading_data (file_id, data_key, data_value, data_type, row_number)
            VALUES (?, ?, ?, ?, ?)
            """;
        
        try (Connection conn = dataSource.getConnection()) {
            conn.setAutoCommit(false);
            
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                
                int batchSize = Integer.parseInt(AppConfig.getInstance().getProperty("data.batch_size", "1000"));
                int count = 0;
                int rowNumber = 1;
                
                for (Map<String, Object> record : dataList) {
                    for (Map.Entry<String, Object> entry : record.entrySet()) {
                        stmt.setLong(1, fileId);
                        stmt.setString(2, entry.getKey());
                        stmt.setString(3, entry.getValue() != null ? entry.getValue().toString() : null);
                        stmt.setString(4, getDataType(entry.getValue()));
                        stmt.setInt(5, rowNumber);
                        
                        stmt.addBatch();
                        count++;
                        
                        if (count % batchSize == 0) {
                            stmt.executeBatch();
                            logger.info("批量保存数据进度: {} 条", count);
                        }
                    }
                    rowNumber++;
                }
                
                // 执行剩余的批次
                stmt.executeBatch();
                conn.commit();
                
                logger.info("数据保存完成，总计: {} 条记录", count);
                
            } catch (SQLException e) {
                conn.rollback();
                throw e;
            }
            
        } catch (SQLException e) {
            logger.error("批量保存数据失败", e);
            throw new RuntimeException("数据保存失败", e);
        }
    }
    
    /**
     * 获取数据类型
     */
    private String getDataType(Object value) {
        if (value == null) {
            return "NULL";
        } else if (value instanceof String) {
            return "STRING";
        } else if (value instanceof Integer || value instanceof Long) {
            return "INTEGER";
        } else if (value instanceof Double || value instanceof Float) {
            return "DECIMAL";
        } else if (value instanceof Boolean) {
            return "BOOLEAN";
        } else if (value instanceof java.util.Date) {
            return "DATE";
        } else {
            return "OBJECT";
        }
    }
    
    /**
     * 查询文件信息
     */
    public List<FileInfo> getFileInfoList() {
        String sql = "SELECT * FROM file_info ORDER BY created_at DESC";
        
        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            List<FileInfo> fileInfoList = new java.util.ArrayList<>();
            
            while (rs.next()) {
                FileInfo fileInfo = new FileInfo();
                fileInfo.setFileName(rs.getString("file_name"));
                fileInfo.setFilePath(rs.getString("file_path"));
                fileInfo.setFileSize(rs.getLong("file_size"));
                fileInfo.setFileType(rs.getString("file_type"));
                fileInfo.setDownloadTime(rs.getTimestamp("download_time").toLocalDateTime());
                fileInfo.setStatus(rs.getString("status"));
                fileInfo.setErrorMessage(rs.getString("error_message"));
                
                fileInfoList.add(fileInfo);
            }
            
            return fileInfoList;
            
        } catch (SQLException e) {
            logger.error("查询文件信息失败", e);
            return new java.util.ArrayList<>();
        }
    }
    
    /**
     * 查询数据统计信息
     */
    public Map<String, Object> getDataStatistics() {
        Map<String, Object> stats = new java.util.HashMap<>();
        
        try (Connection conn = dataSource.getConnection()) {
            
            // 文件总数
            try (PreparedStatement stmt = conn.prepareStatement("SELECT COUNT(*) FROM file_info");
                 ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    stats.put("total_files", rs.getInt(1));
                }
            }
            
            // 数据总数
            try (PreparedStatement stmt = conn.prepareStatement("SELECT COUNT(*) FROM trading_data");
                 ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    stats.put("total_records", rs.getInt(1));
                }
            }
            
            // 按状态统计文件
            try (PreparedStatement stmt = conn.prepareStatement("SELECT status, COUNT(*) FROM file_info GROUP BY status");
                 ResultSet rs = stmt.executeQuery()) {
                Map<String, Integer> statusStats = new java.util.HashMap<>();
                while (rs.next()) {
                    statusStats.put(rs.getString(1), rs.getInt(2));
                }
                stats.put("file_status", statusStats);
            }
            
        } catch (SQLException e) {
            logger.error("查询统计信息失败", e);
        }
        
        return stats;
    }
}
