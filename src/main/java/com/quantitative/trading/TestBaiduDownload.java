package com.quantitative.trading;

import com.quantitative.trading.model.FileInfo;
import com.quantitative.trading.service.BaiduPanService;
import com.quantitative.trading.service.DataProcessorService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

/**
 * 测试百度网盘下载功能
 * 专门用于测试您提供的链接
 */
public class TestBaiduDownload {
    
    private static final Logger logger = LoggerFactory.getLogger(TestBaiduDownload.class);
    
    public static void main(String[] args) {
        logger.info("开始测试百度网盘下载功能");
        
        // 您提供的链接和密码
        String shareUrl = "https://pan.baidu.com/s/1kY8OyapYGLc9CwPy1ik8Iw?pwd=6666";
        String password = "6666";
        
        try {
            // 创建服务实例
            BaiduPanService baiduPanService = new BaiduPanService();
            DataProcessorService dataProcessorService = new DataProcessorService();
            
            logger.info("=== 步骤1: 尝试下载文件 ===");
            logger.info("分享链接: {}", shareUrl);
            logger.info("提取码: {}", password);
            
            // 下载文件
            List<FileInfo> downloadedFiles = baiduPanService.downloadFromShare(shareUrl, password);
            
            if (downloadedFiles.isEmpty()) {
                logger.warn("没有下载到任何文件，请检查链接是否有效或手动下载");
                return;
            }
            
            logger.info("=== 步骤2: 处理下载的文件 ===");
            logger.info("发现 {} 个文件", downloadedFiles.size());
            
            // 处理每个文件
            for (FileInfo fileInfo : downloadedFiles) {
                logger.info("处理文件: {}", fileInfo.getFileName());
                logger.info("文件大小: {} bytes", fileInfo.getFileSize());
                logger.info("文件类型: {}", fileInfo.getFileType());
                logger.info("文件路径: {}", fileInfo.getFilePath());
                
                // 检查文件格式是否支持
                if (isSupportedFormat(fileInfo.getFileType())) {
                    try {
                        // 处理文件数据
                        List<Map<String, Object>> data = dataProcessorService.processFile(fileInfo);
                        
                        if (!data.isEmpty()) {
                            logger.info("成功提取数据: {} 条记录", data.size());
                            
                            // 显示前几条数据作为示例
                            showSampleData(data, fileInfo.getFileName());
                        } else {
                            logger.warn("文件中没有提取到数据");
                        }
                        
                    } catch (Exception e) {
                        logger.error("处理文件失败: {}", fileInfo.getFileName(), e);
                    }
                } else {
                    logger.warn("不支持的文件格式: {}", fileInfo.getFileType());
                }
                
                logger.info("---");
            }
            
            logger.info("=== 测试完成 ===");
            
        } catch (Exception e) {
            logger.error("测试过程中发生错误", e);
        }
    }
    
    /**
     * 检查文件格式是否支持
     */
    private static boolean isSupportedFormat(String fileType) {
        if (fileType == null || fileType.isEmpty()) {
            return false;
        }
        
        String[] supportedFormats = {"csv", "xlsx", "xls", "json", "txt"};
        for (String format : supportedFormats) {
            if (format.equalsIgnoreCase(fileType)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 显示示例数据
     */
    private static void showSampleData(List<Map<String, Object>> data, String fileName) {
        logger.info("=== {} 的数据示例 ===", fileName);
        
        int maxSamples = Math.min(5, data.size()); // 最多显示5条记录
        
        for (int i = 0; i < maxSamples; i++) {
            Map<String, Object> record = data.get(i);
            logger.info("记录 {}: {}", i + 1, record);
        }
        
        if (data.size() > maxSamples) {
            logger.info("... 还有 {} 条记录", data.size() - maxSamples);
        }
        
        // 显示数据结构信息
        if (!data.isEmpty()) {
            Map<String, Object> firstRecord = data.get(0);
            logger.info("数据字段: {}", firstRecord.keySet());
        }
    }
}
