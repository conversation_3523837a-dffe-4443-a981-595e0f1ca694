package com.quantitative.trading;

import com.quantitative.trading.config.AppConfig;
import com.quantitative.trading.model.FileInfo;
import com.quantitative.trading.service.BaiduPanService;
import com.quantitative.trading.service.DataProcessorService;
import com.quantitative.trading.service.DatabaseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

/**
 * 百度网盘下载器主程序
 * 负责协调整个下载和数据处理流程
 */
public class BaiduPanDownloader {
    
    private static final Logger logger = LoggerFactory.getLogger(BaiduPanDownloader.class);
    
    private final BaiduPanService baiduPanService;
    private final DataProcessorService dataProcessorService;
    private final DatabaseService databaseService;
    
    public BaiduPanDownloader() {
        this.baiduPanService = new BaiduPanService();
        this.dataProcessorService = new DataProcessorService();
        this.databaseService = new DatabaseService();
    }
    
    /**
     * 执行完整的下载和数据处理流程
     */
    public void execute(String shareUrl, String password) {
        logger.info("开始执行百度网盘文件下载和数据处理流程");
        logger.info("分享链接: {}", shareUrl);
        logger.info("提取码: {}", password);
        
        try {
            // 步骤1: 下载文件
            logger.info("=== 步骤1: 下载文件 ===");
            List<FileInfo> downloadedFiles = baiduPanService.downloadFromShare(shareUrl, password);
            
            if (downloadedFiles.isEmpty()) {
                logger.warn("没有下载到任何文件");
                return;
            }
            
            logger.info("成功下载 {} 个文件", downloadedFiles.size());
            
            // 步骤2: 处理每个文件
            logger.info("=== 步骤2: 处理文件数据 ===");
            for (FileInfo fileInfo : downloadedFiles) {
                processFile(fileInfo);
            }
            
            // 步骤3: 显示统计信息
            logger.info("=== 步骤3: 处理完成统计 ===");
            showStatistics();
            
            logger.info("所有文件处理完成！");
            
        } catch (Exception e) {
            logger.error("执行过程中发生错误", e);
        }
    }
    
    /**
     * 处理单个文件
     */
    private void processFile(FileInfo fileInfo) {
        logger.info("处理文件: {}", fileInfo.getFileName());
        
        try {
            // 保存文件信息到数据库
            Long fileId = databaseService.saveFileInfo(fileInfo);
            if (fileId == null) {
                logger.error("保存文件信息失败: {}", fileInfo.getFileName());
                return;
            }
            
            // 检查文件格式是否支持
            if (!isSupportedFormat(fileInfo.getFileType())) {
                logger.warn("不支持的文件格式: {}", fileInfo.getFileType());
                fileInfo.setStatus("UNSUPPORTED");
                fileInfo.setErrorMessage("不支持的文件格式: " + fileInfo.getFileType());
                return;
            }
            
            // 处理文件数据
            List<Map<String, Object>> data = dataProcessorService.processFile(fileInfo);
            
            if (data.isEmpty()) {
                logger.warn("文件中没有提取到数据: {}", fileInfo.getFileName());
                fileInfo.setStatus("NO_DATA");
                return;
            }
            
            // 保存数据到数据库
            databaseService.saveData(fileId, data);
            
            fileInfo.setStatus("COMPLETED");
            logger.info("文件处理完成: {} (提取了 {} 条数据)", fileInfo.getFileName(), data.size());
            
        } catch (Exception e) {
            logger.error("处理文件失败: {}", fileInfo.getFileName(), e);
            fileInfo.setStatus("ERROR");
            fileInfo.setErrorMessage(e.getMessage());
        }
    }
    
    /**
     * 检查文件格式是否支持
     */
    private boolean isSupportedFormat(String fileType) {
        String[] supportedFormats = {"csv", "xlsx", "xls", "json", "txt"};
        for (String format : supportedFormats) {
            if (format.equalsIgnoreCase(fileType)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 显示统计信息
     */
    private void showStatistics() {
        // 这里可以添加统计信息的显示
        logger.info("处理统计信息:");
        logger.info("- 详细统计信息请查看数据库");
    }
    
    /**
     * 主程序入口
     */
    public static void main(String[] args) {
        logger.info("启动百度网盘下载器");
        
        try {
            // 从配置文件读取分享链接信息
            AppConfig config = AppConfig.getInstance();
            String shareUrl = config.getProperty("baidu.share.url");
            String password = config.getProperty("baidu.share.password");
            
            if (shareUrl == null || shareUrl.isEmpty()) {
                logger.error("请在配置文件中设置百度网盘分享链接");
                return;
            }
            
            if (password == null || password.isEmpty()) {
                logger.error("请在配置文件中设置提取码");
                return;
            }
            
            // 创建下载器并执行
            BaiduPanDownloader downloader = new BaiduPanDownloader();
            downloader.execute(shareUrl, password);
            
        } catch (Exception e) {
            logger.error("程序执行失败", e);
            System.exit(1);
        }
    }
}
