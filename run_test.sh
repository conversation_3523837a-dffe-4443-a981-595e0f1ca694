#!/bin/bash

# 百度网盘下载测试脚本
# 用于测试您提供的百度网盘链接

echo "=== 百度网盘下载测试 ==="
echo "链接: https://pan.baidu.com/s/1kY8OyapYGLc9CwPy1ik8Iw?pwd=6666"
echo "提取码: 6666"
echo ""

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java环境，请安装Java 17或更高版本"
    exit 1
fi

# 检查Maven环境
if ! command -v mvn &> /dev/null; then
    echo "错误: 未找到Maven，请安装Maven 3.6或更高版本"
    exit 1
fi

echo "正在编译项目..."
mvn clean compile -q

if [ $? -ne 0 ]; then
    echo "编译失败，请检查错误信息"
    exit 1
fi

echo "编译完成，开始测试下载..."
echo ""

# 创建必要的目录
mkdir -p temp
mkdir -p data
mkdir -p logs

# 运行测试
mvn exec:java -Dexec.mainClass="com.quantitative.trading.TestBaiduDownload" -q

echo ""
echo "测试完成！"
echo ""
echo "如果自动下载失败，请按照程序提示手动下载文件。"
echo "下载目录: ./temp"
echo "日志文件: ./logs/app.log"
