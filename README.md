# 量化交易数据处理系统

这是一个专门用于从百度网盘下载文件并将数据写入数据库的Java应用程序。

## 功能特性

- ✅ 支持百度网盘分享链接下载
- ✅ 支持多种数据格式（CSV、Excel、JSON、TXT）
- ✅ 自动数据解析和类型识别
- ✅ 批量数据库存储
- ✅ 完整的日志记录
- ✅ 多种下载方式（官方API、第三方工具、手动下载）

## 支持的文件格式

- **CSV**: 逗号分隔值文件
- **Excel**: .xlsx 和 .xls 文件
- **JSON**: JSON格式数据文件
- **TXT**: 纯文本文件

## 快速开始

### 1. 环境要求

- Java 17 或更高版本
- Maven 3.6 或更高版本
- MySQL 数据库（可选，也支持其他数据库）

### 2. 配置数据库

在MySQL中创建数据库：

```sql
CREATE DATABASE quantitative_trading CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 3. 配置应用

编辑 `src/main/resources/application.yml` 文件，修改数据库连接信息：

```yaml
database:
  url: "*******************************************************************************************************"
  username: "your_username"
  password: "your_password"
  driver: "com.mysql.cj.jdbc.Driver"
```

### 4. 编译项目

```bash
mvn clean compile
```

### 5. 运行测试

测试您的百度网盘链接：

```bash
mvn exec:java -Dexec.mainClass="com.quantitative.trading.TestBaiduDownload"
```

### 6. 运行完整程序

```bash
mvn exec:java -Dexec.mainClass="com.quantitative.trading.BaiduPanDownloader"
```

## 使用方法

### 方法1: 使用配置文件

在 `application.yml` 中配置您的百度网盘链接：

```yaml
baidu:
  share:
    url: "https://pan.baidu.com/s/1kY8OyapYGLc9CwPy1ik8Iw?pwd=6666"
    password: "6666"
```

然后运行主程序。

### 方法2: 手动下载

如果自动下载不工作，程序会指导您手动下载：

1. 程序会显示百度网盘链接和提取码
2. 在浏览器中打开链接并下载文件
3. 将文件保存到程序指定的目录
4. 按回车键继续处理

## 下载方式

程序支持多种下载方式，按优先级排序：

### 1. 百度网盘官方API（推荐）
- 需要企业开发者认证
- 最稳定可靠的方式
- 支持大文件和批量下载

### 2. BaiduPCS-Go 第三方工具
- 开源的百度网盘命令行工具
- 需要单独安装
- 安装方法：
  ```bash
  # macOS
  brew install baidupcs-go
  
  # 或者从GitHub下载
  # https://github.com/qjfoidnh/BaiduPCS-Go
  ```

### 3. 手动下载
- 最可靠的备选方案
- 程序会提供详细指导
- 适合所有情况

## 数据库结构

程序会自动创建以下表：

### file_info 表
存储文件信息：
- id: 主键
- file_name: 文件名
- file_path: 文件路径
- file_size: 文件大小
- file_type: 文件类型
- download_time: 下载时间
- status: 处理状态
- error_message: 错误信息

### trading_data 表
存储解析的数据：
- id: 主键
- file_id: 关联的文件ID
- data_key: 数据字段名
- data_value: 数据值
- data_type: 数据类型
- row_number: 行号

## 配置选项

### 数据库配置
```yaml
database:
  url: "数据库连接URL"
  username: "用户名"
  password: "密码"
  driver: "数据库驱动"
```

### 百度网盘配置
```yaml
baidu:
  # 官方API配置
  api:
    app_key: "your_app_key"
    secret_key: "your_secret_key"
    
  # 第三方工具配置
  pcs:
    tool_path: "/usr/local/bin/BaiduPCS-Go"
    
  # 分享链接配置
  share:
    url: "分享链接"
    password: "提取码"
```

### 下载配置
```yaml
download:
  temp_dir: "./temp"      # 临时下载目录
  target_dir: "./data"    # 目标数据目录
  max_retry: 3            # 最大重试次数
  timeout: 300000         # 超时时间（毫秒）
```

## 故障排除

### 1. 数据库连接失败
- 检查数据库是否启动
- 验证连接信息是否正确
- 确认数据库用户权限

### 2. 文件下载失败
- 检查网络连接
- 验证分享链接是否有效
- 尝试手动下载方式

### 3. 文件解析失败
- 检查文件格式是否支持
- 验证文件是否损坏
- 查看详细错误日志

## 日志

程序会在以下位置生成日志：
- 控制台输出：实时日志
- 文件日志：`./logs/app.log`

## 开发和扩展

### 添加新的文件格式支持

1. 在 `DataProcessorService` 中添加新的处理方法
2. 在配置文件中添加支持的格式
3. 更新文档

### 添加新的下载方式

1. 在 `BaiduPanService` 中实现新的下载方法
2. 在下载流程中集成新方法
3. 添加相应的配置选项

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
